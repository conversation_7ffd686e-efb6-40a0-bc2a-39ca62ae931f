{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\testDynamoDB\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{B4A0B0BD-A3BC-4A53-88CB-99E0D65D285D}|testDynamoDB\\testDynamoDB.csproj|c:\\users\\<USER>\\desktop\\test\\testvs\\testdynamodb\\testdynamodb\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{B4A0B0BD-A3BC-4A53-88CB-99E0D65D285D}|testDynamoDB\\testDynamoDB.csproj|solutionrelative:testdynamodb\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{B4A0B0BD-A3BC-4A53-88CB-99E0D65D285D}|testDynamoDB\\testDynamoDB.csproj|c:\\users\\<USER>\\desktop\\test\\testvs\\testdynamodb\\testdynamodb\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B4A0B0BD-A3BC-4A53-88CB-99E0D65D285D}|testDynamoDB\\testDynamoDB.csproj|solutionrelative:testdynamodb\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B4A0B0BD-A3BC-4A53-88CB-99E0D65D285D}|testDynamoDB\\testDynamoDB.csproj|c:\\users\\<USER>\\desktop\\test\\testvs\\testdynamodb\\testdynamodb\\views\\home\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B4A0B0BD-A3BC-4A53-88CB-99E0D65D285D}|testDynamoDB\\testDynamoDB.csproj|solutionrelative:testdynamodb\\views\\home\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 164, "DocumentGroups": [{"DockedWidth": 203, "SelectedChildIndex": 3, "Children": [{"$type": "Bookmark", "Name": "ST:129:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:128:0:{75188d03-9892-4ae2-abf1-207126247ce5}"}, {"$type": "Bookmark", "Name": "ST:130:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "appsettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\testDynamoDB\\testDynamoDB\\appsettings.json", "RelativeDocumentMoniker": "testDynamoDB\\appsettings.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\testDynamoDB\\testDynamoDB\\appsettings.json*", "RelativeToolTip": "testDynamoDB\\appsettings.json*", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-05-30T03:31:53.495Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\testDynamoDB\\testDynamoDB\\Program.cs", "RelativeDocumentMoniker": "testDynamoDB\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\testDynamoDB\\testDynamoDB\\Program.cs", "RelativeToolTip": "testDynamoDB\\Program.cs", "ViewState": "AgIAACEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-30T03:31:44.284Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "Index.cshtml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\testDynamoDB\\testDynamoDB\\Views\\Home\\Index.cshtml", "RelativeDocumentMoniker": "testDynamoDB\\Views\\Home\\Index.cshtml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\testDynamoDB\\testDynamoDB\\Views\\Home\\Index.cshtml", "RelativeToolTip": "testDynamoDB\\Views\\Home\\Index.cshtml", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-05-30T03:29:05.129Z"}]}, {"DockedWidth": 1205, "SelectedChildIndex": -1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}, {"$type": "Bookmark", "Name": "ST:0:0:{34e76e81-ee4a-11d0-ae2e-00a0c90fffc3}"}]}, {"DockedWidth": 462, "SelectedChildIndex": -1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{0ad07096-bba9-4900-a651-0598d26f6d24}"}]}]}]}