# DynamoDB Local Setup Guide

This guide explains how to set up and configure DynamoDB Local for the Customer Feedback Management System.

## Prerequisites

- Java 8 or higher installed on your system
- DynamoDB Local downloaded and extracted

## DynamoDB Local Configuration

The application is configured to connect to DynamoDB Local with these specific settings:

- **Port**: 8000
- **InMemory**: false (data persists to disk)
- **Version**: 2.6.1
- **SharedDb**: true (allows multiple applications to share the same database)
- **Region**: us-east-1
- **Access Key**: "key"
- **Secret Key**: "key"
- **Endpoint**: http://localhost:8000

## Starting DynamoDB Local

### Option 1: Command Line (Recommended)

Navigate to your DynamoDB Local directory and run:

```bash
java -Djava.library.path=./DynamoDBLocal_lib -jar DynamoDBLocal.jar -port 8000 -sharedDb
```

### Option 2: With Specific Data Directory

To specify a custom data directory:

```bash
java -Djava.library.path=./DynamoDBLocal_lib -jar DynamoDBLocal.jar -port 8000 -sharedDb -dbPath ./data
```

### Option 3: In-Memory Mode (Data Not Persisted)

For testing purposes only:

```bash
java -Djava.library.path=./DynamoDBLocal_lib -jar DynamoDBLocal.jar -port 8000 -inMemory
```

## Verifying DynamoDB Local is Running

1. **Check the console output** - You should see:
   ```
   Initializing DynamoDB Local with the following configuration:
   Port: 8000
   InMemory: false
   DbPath: null
   SharedDb: true
   shouldDelayTransientStatuses: false
   CorsParams: *
   ```

2. **Test the endpoint** - Open a browser and navigate to:
   ```
   http://localhost:8000/shell/
   ```
   You should see the DynamoDB Local web shell.

3. **Using AWS CLI** (if installed):
   ```bash
   aws dynamodb list-tables --endpoint-url http://localhost:8000 --region us-east-1
   ```

## Table Schema

The application automatically creates the `CustomerFeedback` table with the following schema:

### Table Definition
- **Table Name**: CustomerFeedback
- **Partition Key**: Id (String)
- **Billing Mode**: PAY_PER_REQUEST (On-Demand)

### Attributes
- **Id**: String (Primary Key) - Unique identifier (GUID)
- **CustomerName**: String - Customer's full name
- **Email**: String - Customer's email address
- **FeedbackText**: String - The feedback content
- **Rating**: Number - Rating from 1 to 5
- **Category**: String - Feedback category (Product, Service, Support, Website, Other)
- **Status**: String - Current status (Open, In Progress, Resolved, Closed)
- **CreatedAt**: String (ISO DateTime) - Creation timestamp
- **UpdatedAt**: String (ISO DateTime) - Last update timestamp

## Manual Table Creation (Optional)

If you need to create the table manually, you can use the AWS CLI:

```bash
aws dynamodb create-table \
    --table-name CustomerFeedback \
    --attribute-definitions \
        AttributeName=Id,AttributeType=S \
    --key-schema \
        AttributeName=Id,KeyType=HASH \
    --billing-mode PAY_PER_REQUEST \
    --endpoint-url http://localhost:8000 \
    --region us-east-1
```

## Data Management

### Viewing Data

You can view the data using:

1. **DynamoDB Local Web Shell**: http://localhost:8000/shell/
2. **AWS CLI**:
   ```bash
   aws dynamodb scan --table-name CustomerFeedback --endpoint-url http://localhost:8000 --region us-east-1
   ```

### Backing Up Data

Since we're using persistent storage (not in-memory), your data is automatically saved to disk. The data files are stored in the DynamoDB Local directory.

### Clearing Data

To clear all data:

1. **Delete the table**:
   ```bash
   aws dynamodb delete-table --table-name CustomerFeedback --endpoint-url http://localhost:8000 --region us-east-1
   ```

2. **Restart the application** - It will recreate the table automatically.

## Troubleshooting

### Common Issues

1. **Port 8000 already in use**:
   - Change the port in both DynamoDB Local startup command and `appsettings.json`
   - Example: Use port 8001
   ```bash
   java -Djava.library.path=./DynamoDBLocal_lib -jar DynamoDBLocal.jar -port 8001 -sharedDb
   ```

2. **Java not found**:
   - Ensure Java 8+ is installed and in your PATH
   - Check with: `java -version`

3. **Permission denied**:
   - Ensure you have write permissions in the DynamoDB Local directory
   - On Linux/Mac: `chmod +x DynamoDBLocal.jar`

4. **Connection refused**:
   - Verify DynamoDB Local is running
   - Check the port number in the application configuration
   - Ensure no firewall is blocking the connection

### Logs and Debugging

DynamoDB Local outputs logs to the console. Common log messages:

- **Successful startup**: "Initializing DynamoDB Local..."
- **Table operations**: Shows CREATE, PUT, GET, SCAN operations
- **Errors**: Connection issues, malformed requests

## Performance Considerations

- **Persistent vs In-Memory**: Persistent storage is slower but data survives restarts
- **SharedDb**: Allows multiple applications to use the same DynamoDB Local instance
- **Data Size**: DynamoDB Local can handle reasonable amounts of test data
- **Concurrent Access**: Multiple applications can access the same instance safely

## Security Notes

- DynamoDB Local is for development/testing only
- No authentication is required (uses dummy credentials)
- Do not use in production environments
- Data is stored locally and not encrypted

## Next Steps

After setting up DynamoDB Local:

1. Start DynamoDB Local using the command above
2. Run the Customer Feedback Management application
3. The application will automatically create the required table
4. Begin adding and managing customer feedback

For more information about DynamoDB Local, visit the [AWS Documentation](https://docs.aws.amazon.com/amazondynamodb/latest/developerguide/DynamoDBLocal.html).
