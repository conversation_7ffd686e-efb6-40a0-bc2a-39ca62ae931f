# Customer Feedback Management System

A web-based customer feedback management system built with ASP.NET Core and DynamoDB Local. This application allows you to create, read, update, and delete customer feedback entries with a clean, modern interface.

## Features

- **CRUD Operations**: Create, read, update, and delete customer feedback
- **Search & Filter**: Search by customer name, email, or feedback text
- **Category Management**: Organize feedback by categories (Product, Service, Support, Website, Other)
- **Rating System**: 5-star rating system for feedback
- **Status Tracking**: Track feedback status (Open, In Progress, Resolved, Closed)
- **Responsive Design**: Mobile-friendly interface with Bootstrap
- **Real-time Data**: Uses DynamoDB Local for fast, local data storage

## Prerequisites

Before running this application, ensure you have:

1. **.NET 9.0 SDK** installed
2. **DynamoDB Local** running on port 8000
3. **Visual Studio 2022** or **Visual Studio Code** (optional)

## DynamoDB Local Setup

This application is configured to work with DynamoDB Local running with these settings:

- **Port**: 8000
- **InMemory**: false
- **Version**: 2.6.1
- **SharedDb**: true
- **Region**: us-east-1
- **Access Key**: "key"
- **Secret Key**: "key"
- **Endpoint**: http://localhost:8000

### Starting DynamoDB Local

If you haven't already started DynamoDB Local, you can start it with:

```bash
java -Djava.library.path=./DynamoDBLocal_lib -jar DynamoDBLocal.jar -port 8000 -sharedDb
```

## Installation & Setup

1. **Clone or navigate to the project directory**:
   ```bash
   cd testDynamoDB
   ```

2. **Restore NuGet packages**:
   ```bash
   dotnet restore
   ```

3. **Build the application**:
   ```bash
   dotnet build
   ```

4. **Run the application**:
   ```bash
   dotnet run
   ```

5. **Open your browser** and navigate to:
   - HTTPS: `https://localhost:7000`
   - HTTP: `http://localhost:5000`

## Configuration

The application configuration is stored in `appsettings.json`:

```json
{
  "DynamoDB": {
    "ServiceURL": "http://localhost:8000",
    "AccessKey": "key",
    "SecretKey": "key",
    "Region": "us-east-1"
  }
}
```

## Database Schema

The application automatically creates a DynamoDB table called `CustomerFeedback` with the following structure:

- **Id** (String, Hash Key): Unique identifier for each feedback
- **CustomerName** (String): Name of the customer
- **Email** (String): Customer's email address
- **FeedbackText** (String): The actual feedback content
- **Rating** (Number): Rating from 1-5 stars
- **Category** (String): Feedback category
- **Status** (String): Current status of the feedback
- **CreatedAt** (DateTime): When the feedback was created
- **UpdatedAt** (DateTime): When the feedback was last updated

## Usage

### Adding New Feedback

1. Click "Add New Feedback" button or use the navigation menu
2. Fill in the required fields:
   - Customer Name
   - Email Address
   - Feedback Text
   - Rating (1-5 stars)
   - Category
3. Click "Save Feedback"

### Viewing Feedback

- The main page displays all feedback in a table format
- Use the search and filter options to find specific feedback
- Click the eye icon to view detailed feedback information

### Editing Feedback

1. Click the edit icon next to any feedback entry
2. Modify the fields as needed
3. Update the status if required
4. Click "Update Feedback"

### Deleting Feedback

1. Click the delete icon next to any feedback entry
2. Review the feedback details on the confirmation page
3. Click "Confirm Delete" to permanently remove the feedback

### Search and Filter

Use the search and filter form to find specific feedback:

- **Search**: Enter text to search in customer name, email, or feedback content
- **Category Filter**: Filter by feedback category
- **Status Filter**: Filter by feedback status
- **Rating Filter**: Filter by star rating
- **Clear**: Reset all filters

## Project Structure

```
testDynamoDB/
├── Controllers/
│   ├── FeedbackController.cs    # Main feedback operations
│   └── HomeController.cs        # Home page controller
├── Models/
│   ├── CustomerFeedback.cs      # Main feedback model
│   ├── FeedbackViewModel.cs     # View models for forms
│   └── ErrorViewModel.cs        # Error handling model
├── Services/
│   ├── IDynamoDbService.cs      # DynamoDB service interface
│   └── DynamoDbService.cs       # DynamoDB service implementation
├── Views/
│   ├── Feedback/                # All feedback-related views
│   │   ├── Index.cshtml         # Feedback list page
│   │   ├── Create.cshtml        # Create feedback form
│   │   ├── Edit.cshtml          # Edit feedback form
│   │   ├── Details.cshtml       # Feedback details page
│   │   └── Delete.cshtml        # Delete confirmation page
│   └── Shared/
│       └── _Layout.cshtml       # Main layout template
├── wwwroot/
│   └── css/
│       └── site.css             # Custom styles
├── appsettings.json             # Application configuration
└── Program.cs                   # Application startup
```

## Technologies Used

- **ASP.NET Core 9.0**: Web framework
- **AWS SDK for .NET**: DynamoDB integration
- **Bootstrap 5**: UI framework
- **Font Awesome**: Icons
- **jQuery**: JavaScript functionality

## Troubleshooting

### Common Issues

1. **DynamoDB Connection Error**:
   - Ensure DynamoDB Local is running on port 8000
   - Check the connection settings in `appsettings.json`

2. **Table Creation Error**:
   - The application automatically creates the table on startup
   - Check DynamoDB Local logs for any errors

3. **Build Errors**:
   - Run `dotnet restore` to ensure all packages are installed
   - Check that .NET 9.0 SDK is installed

### Logs

The application uses built-in .NET logging. Check the console output for any error messages or debugging information.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is for demonstration purposes. Feel free to use and modify as needed.
