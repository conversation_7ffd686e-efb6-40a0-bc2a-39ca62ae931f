# Script to check if DynamoDB Local is running and provide instructions

Write-Host "Checking DynamoDB Local Status..." -ForegroundColor Yellow

try {
    $response = Invoke-WebRequest -Uri "http://localhost:8000" -Method GET -TimeoutSec 5
    Write-Host "✓ DynamoDB Local is running on port 8000" -ForegroundColor Green
    
    # Test if we can list tables
    try {
        $env:AWS_ACCESS_KEY_ID = "key"
        $env:AWS_SECRET_ACCESS_KEY = "key"
        $env:AWS_DEFAULT_REGION = "us-east-1"
        
        $tables = aws dynamodb list-tables --endpoint-url http://localhost:8000 --region us-east-1 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ DynamoDB Local is responding to AWS CLI commands" -ForegroundColor Green
            Write-Host "Current tables: $tables" -ForegroundColor Cyan
        } else {
            Write-Host "⚠ DynamoDB Local is running but AWS CLI test failed" -ForegroundColor Yellow
            Write-Host "This might be normal if AWS CLI is not installed" -ForegroundColor Gray
        }
    } catch {
        Write-Host "⚠ Could not test AWS CLI (this is optional)" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "✗ DynamoDB Local is NOT running on port 8000" -ForegroundColor Red
    Write-Host ""
    Write-Host "To start DynamoDB Local, you need to:" -ForegroundColor Yellow
    Write-Host "1. Download DynamoDB Local from AWS if you haven't already" -ForegroundColor White
    Write-Host "2. Extract it to a folder" -ForegroundColor White
    Write-Host "3. Run this command in the DynamoDB Local folder:" -ForegroundColor White
    Write-Host ""
    Write-Host "   java -Djava.library.path=./DynamoDBLocal_lib -jar DynamoDBLocal.jar -port 8000 -sharedDb" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Alternative commands:" -ForegroundColor Yellow
    Write-Host "- For in-memory mode (data not persisted):" -ForegroundColor White
    Write-Host "   java -Djava.library.path=./DynamoDBLocal_lib -jar DynamoDBLocal.jar -port 8000 -inMemory" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "- For custom data directory:" -ForegroundColor White
    Write-Host "   java -Djava.library.path=./DynamoDBLocal_lib -jar DynamoDBLocal.jar -port 8000 -sharedDb -dbPath ./data" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Download DynamoDB Local from:" -ForegroundColor Yellow
    Write-Host "https://docs.aws.amazon.com/amazondynamodb/latest/developerguide/DynamoDBLocal.DownloadingAndRunning.html" -ForegroundColor Blue
    Write-Host ""
    Write-Host "After starting DynamoDB Local, run your application again with:" -ForegroundColor Yellow
    Write-Host "   cd testDynamoDB" -ForegroundColor Cyan
    Write-Host "   dotnet run" -ForegroundColor Cyan
}

Write-Host ""
Write-Host "Current application configuration:" -ForegroundColor Yellow
try {
    $config = Get-Content "testDynamoDB/appsettings.json" | ConvertFrom-Json
    Write-Host "- Endpoint: $($config.DynamoDB.ServiceURL)" -ForegroundColor White
    Write-Host "- Region: $($config.DynamoDB.Region)" -ForegroundColor White
    Write-Host "- Access Key: $($config.DynamoDB.AccessKey)" -ForegroundColor White
} catch {
    Write-Host "Could not read application configuration" -ForegroundColor Red
}
