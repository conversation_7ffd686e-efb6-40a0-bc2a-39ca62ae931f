Write-Host "Testing DynamoDB Customer Feedback App..." -ForegroundColor Green

# Change to project directory
Set-Location "testDynamoDB"

Write-Host "Step 1: Restore packages..." -ForegroundColor Yellow
$restoreResult = dotnet restore 2>&1
if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ Restore successful" -ForegroundColor Green
} else {
    Write-Host "✗ Restore failed:" -ForegroundColor Red
    Write-Host $restoreResult -ForegroundColor Red
    Set-Location ".."
    exit 1
}

Write-Host "Step 2: Build project..." -ForegroundColor Yellow
$buildResult = dotnet build --no-restore 2>&1
if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ Build successful" -ForegroundColor Green
} else {
    Write-Host "✗ Build failed:" -ForegroundColor Red
    Write-Host $buildResult -ForegroundColor Red
    Set-Location ".."
    exit 1
}

Write-Host "Step 3: Check DynamoDB Local..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8000" -Method GET -TimeoutSec 3 -ErrorAction Stop
    Write-Host "✓ DynamoDB Local is running" -ForegroundColor Green
} catch {
    Write-Host "✗ DynamoDB Local is not running on port 8000" -ForegroundColor Red
    Write-Host "Please start DynamoDB Local first:" -ForegroundColor Yellow
    Write-Host "java -Djava.library.path=./DynamoDBLocal_lib -jar DynamoDBLocal.jar -port 8000 -sharedDb" -ForegroundColor Cyan
    Set-Location ".."
    exit 1
}

Write-Host "Step 4: Start application..." -ForegroundColor Yellow
Write-Host "Starting application in background..." -ForegroundColor Cyan
Write-Host "You can access it at: https://localhost:7009 or http://localhost:5153" -ForegroundColor Green

# Start the application
Start-Process -FilePath "dotnet" -ArgumentList "run" -NoNewWindow

Write-Host "Application should be starting..." -ForegroundColor Green
Write-Host "Check the console for any errors." -ForegroundColor Yellow

Set-Location ".."
