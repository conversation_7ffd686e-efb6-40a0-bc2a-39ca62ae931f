# Test script to build and verify the Customer Feedback Management System

Write-Host "Customer Feedback Management System - Build Test" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green

# Change to project directory
Set-Location "testDynamoDB"

Write-Host "`nStep 1: Restoring NuGet packages..." -ForegroundColor Yellow
try {
    dotnet restore
    Write-Host "✓ Package restore completed successfully" -ForegroundColor Green
} catch {
    Write-Host "✗ Package restore failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`nStep 2: Building the application..." -ForegroundColor Yellow
try {
    dotnet build --no-restore
    Write-Host "✓ Build completed successfully" -ForegroundColor Green
} catch {
    Write-Host "✗ Build failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`nStep 3: Checking project structure..." -ForegroundColor Yellow

$requiredFiles = @(
    "Controllers\FeedbackController.cs",
    "Models\CustomerFeedback.cs",
    "Models\FeedbackViewModel.cs",
    "Services\IDynamoDbService.cs",
    "Services\DynamoDbService.cs",
    "Views\Feedback\Index.cshtml",
    "Views\Feedback\Create.cshtml",
    "Views\Feedback\Edit.cshtml",
    "Views\Feedback\Details.cshtml",
    "Views\Feedback\Delete.cshtml",
    "appsettings.json",
    "Program.cs"
)

$allFilesExist = $true
foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "✓ $file" -ForegroundColor Green
    } else {
        Write-Host "✗ $file (missing)" -ForegroundColor Red
        $allFilesExist = $false
    }
}

if ($allFilesExist) {
    Write-Host "`n✓ All required files are present" -ForegroundColor Green
} else {
    Write-Host "`n✗ Some required files are missing" -ForegroundColor Red
    exit 1
}

Write-Host "`nStep 4: Checking configuration..." -ForegroundColor Yellow
$config = Get-Content "appsettings.json" | ConvertFrom-Json
if ($config.DynamoDB.ServiceURL -eq "http://localhost:8000") {
    Write-Host "✓ DynamoDB configuration is correct" -ForegroundColor Green
} else {
    Write-Host "✗ DynamoDB configuration issue" -ForegroundColor Red
}

Write-Host "`n=================================================" -ForegroundColor Green
Write-Host "Build test completed successfully!" -ForegroundColor Green
Write-Host "`nNext steps:" -ForegroundColor Yellow
Write-Host "1. Start DynamoDB Local on port 8000" -ForegroundColor White
Write-Host "2. Run: dotnet run" -ForegroundColor White
Write-Host "3. Open browser to: https://localhost:7000 or http://localhost:5000" -ForegroundColor White
Write-Host "`nFor DynamoDB Local setup, see: DynamoDB-Setup.md" -ForegroundColor Cyan

# Return to original directory
Set-Location ".."
