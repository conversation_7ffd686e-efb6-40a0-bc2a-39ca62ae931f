using Microsoft.AspNetCore.Mvc;
using testDynamoDB.Models;
using testDynamoDB.Services;

namespace testDynamoDB.Controllers;

public class FeedbackController : Controller
{
    private readonly IDynamoDbService _dynamoDbService;
    private readonly ILogger<FeedbackController> _logger;

    public FeedbackController(IDynamoDbService dynamoDbService, ILogger<FeedbackController> logger)
    {
        _dynamoDbService = dynamoDbService;
        _logger = logger;
    }

    // GET: Feedback
    public async Task<IActionResult> Index(string searchTerm = "", string categoryFilter = "", string statusFilter = "", int? ratingFilter = null)
    {
        try
        {
            List<CustomerFeedback> feedbacks;
            
            if (!string.IsNullOrEmpty(searchTerm) || !string.IsNullOrEmpty(categoryFilter) || 
                !string.IsNullOrEmpty(statusFilter) || ratingFilter.HasValue)
            {
                feedbacks = await _dynamoDbService.SearchFeedbackAsync(searchTerm, categoryFilter, statusFilter, ratingFilter);
            }
            else
            {
                feedbacks = await _dynamoDbService.GetAllFeedbackAsync();
            }

            var viewModel = new FeedbackListViewModel
            {
                Feedbacks = feedbacks,
                SearchTerm = searchTerm,
                CategoryFilter = categoryFilter,
                StatusFilter = statusFilter,
                RatingFilter = ratingFilter
            };

            return View(viewModel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading feedback list");
            TempData["Error"] = "Error loading feedback list. Please try again.";
            return View(new FeedbackListViewModel());
        }
    }

    // GET: Feedback/Details/5
    public async Task<IActionResult> Details(string id)
    {
        if (string.IsNullOrEmpty(id))
        {
            return NotFound();
        }

        try
        {
            var feedback = await _dynamoDbService.GetFeedbackByIdAsync(id);
            if (feedback == null)
            {
                return NotFound();
            }

            var viewModel = new FeedbackDetailsViewModel { Feedback = feedback };
            return View(viewModel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error loading feedback details for ID: {id}");
            TempData["Error"] = "Error loading feedback details. Please try again.";
            return RedirectToAction(nameof(Index));
        }
    }

    // GET: Feedback/Create
    public IActionResult Create()
    {
        return View(new CreateFeedbackViewModel());
    }

    // POST: Feedback/Create
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Create(CreateFeedbackViewModel viewModel)
    {
        if (!ModelState.IsValid)
        {
            return View(viewModel);
        }

        try
        {
            var feedback = new CustomerFeedback
            {
                CustomerName = viewModel.CustomerName,
                Email = viewModel.Email,
                FeedbackText = viewModel.FeedbackText,
                Rating = viewModel.Rating,
                Category = viewModel.Category
            };

            await _dynamoDbService.CreateFeedbackAsync(feedback);
            TempData["Success"] = "Feedback created successfully!";
            return RedirectToAction(nameof(Index));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating feedback");
            TempData["Error"] = "Error creating feedback. Please try again.";
            return View(viewModel);
        }
    }

    // GET: Feedback/Edit/5
    public async Task<IActionResult> Edit(string id)
    {
        if (string.IsNullOrEmpty(id))
        {
            return NotFound();
        }

        try
        {
            var feedback = await _dynamoDbService.GetFeedbackByIdAsync(id);
            if (feedback == null)
            {
                return NotFound();
            }

            var viewModel = new EditFeedbackViewModel
            {
                Id = feedback.Id,
                CustomerName = feedback.CustomerName,
                Email = feedback.Email,
                FeedbackText = feedback.FeedbackText,
                Rating = feedback.Rating,
                Category = feedback.Category,
                Status = feedback.Status,
                CreatedAt = feedback.CreatedAt
            };

            return View(viewModel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error loading feedback for edit: {id}");
            TempData["Error"] = "Error loading feedback for editing. Please try again.";
            return RedirectToAction(nameof(Index));
        }
    }

    // POST: Feedback/Edit/5
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Edit(string id, EditFeedbackViewModel viewModel)
    {
        if (id != viewModel.Id)
        {
            return NotFound();
        }

        if (!ModelState.IsValid)
        {
            return View(viewModel);
        }

        try
        {
            var existingFeedback = await _dynamoDbService.GetFeedbackByIdAsync(id);
            if (existingFeedback == null)
            {
                return NotFound();
            }

            existingFeedback.CustomerName = viewModel.CustomerName;
            existingFeedback.Email = viewModel.Email;
            existingFeedback.FeedbackText = viewModel.FeedbackText;
            existingFeedback.Rating = viewModel.Rating;
            existingFeedback.Category = viewModel.Category;
            existingFeedback.Status = viewModel.Status;

            await _dynamoDbService.UpdateFeedbackAsync(existingFeedback);
            TempData["Success"] = "Feedback updated successfully!";
            return RedirectToAction(nameof(Index));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error updating feedback: {id}");
            TempData["Error"] = "Error updating feedback. Please try again.";
            return View(viewModel);
        }
    }

    // GET: Feedback/Delete/5
    public async Task<IActionResult> Delete(string id)
    {
        if (string.IsNullOrEmpty(id))
        {
            return NotFound();
        }

        try
        {
            var feedback = await _dynamoDbService.GetFeedbackByIdAsync(id);
            if (feedback == null)
            {
                return NotFound();
            }

            var viewModel = new FeedbackDetailsViewModel { Feedback = feedback };
            return View(viewModel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error loading feedback for deletion: {id}");
            TempData["Error"] = "Error loading feedback for deletion. Please try again.";
            return RedirectToAction(nameof(Index));
        }
    }

    // POST: Feedback/Delete/5
    [HttpPost, ActionName("Delete")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> DeleteConfirmed(string id)
    {
        try
        {
            var success = await _dynamoDbService.DeleteFeedbackAsync(id);
            if (success)
            {
                TempData["Success"] = "Feedback deleted successfully!";
            }
            else
            {
                TempData["Error"] = "Error deleting feedback. Please try again.";
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error deleting feedback: {id}");
            TempData["Error"] = "Error deleting feedback. Please try again.";
        }

        return RedirectToAction(nameof(Index));
    }
}
