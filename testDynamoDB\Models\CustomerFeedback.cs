using Amazon.DynamoDBv2.DataModel;

namespace testDynamoDB.Models;

[DynamoDBTable("CustomerFeedback")]
public class CustomerFeedback
{
    [DynamoDBHashKey("Id")]
    public string Id { get; set; } = string.Empty;

    [DynamoDBProperty("CustomerName")]
    public string CustomerName { get; set; } = string.Empty;

    [DynamoDBProperty("Email")]
    public string Email { get; set; } = string.Empty;

    [DynamoDBProperty("FeedbackText")]
    public string FeedbackText { get; set; } = string.Empty;

    [DynamoDBProperty("Rating")]
    public int Rating { get; set; }

    [DynamoDBProperty("Category")]
    public string Category { get; set; } = string.Empty;

    [DynamoDBProperty("CreatedAt")]
    public DateTime CreatedAt { get; set; }

    [DynamoDBProperty("UpdatedAt")]
    public DateTime UpdatedAt { get; set; }

    [DynamoDBProperty("Status")]
    public string Status { get; set; } = "Open";

    public CustomerFeedback()
    {
        Id = Guid.NewGuid().ToString();
        CreatedAt = DateTime.UtcNow;
        UpdatedAt = DateTime.UtcNow;
    }
}
