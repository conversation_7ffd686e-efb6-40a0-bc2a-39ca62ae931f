using System.ComponentModel.DataAnnotations;

namespace testDynamoDB.Models;

public class CreateFeedbackViewModel
{
    [Required(ErrorMessage = "Customer name is required")]
    [StringLength(100, ErrorMessage = "Customer name cannot exceed 100 characters")]
    public string CustomerName { get; set; } = string.Empty;

    [Required(ErrorMessage = "Email is required")]
    [EmailAddress(ErrorMessage = "Please enter a valid email address")]
    public string Email { get; set; } = string.Empty;

    [Required(ErrorMessage = "Feedback text is required")]
    [StringLength(1000, ErrorMessage = "Feedback cannot exceed 1000 characters")]
    public string FeedbackText { get; set; } = string.Empty;

    [Required(ErrorMessage = "Rating is required")]
    [Range(1, 5, ErrorMessage = "Rating must be between 1 and 5")]
    public int Rating { get; set; }

    [Required(ErrorMessage = "Category is required")]
    public string Category { get; set; } = string.Empty;
}

public class EditFeedbackViewModel : CreateFeedbackViewModel
{
    public string Id { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
}

public class FeedbackListViewModel
{
    public List<CustomerFeedback> Feedbacks { get; set; } = new();
    public string SearchTerm { get; set; } = string.Empty;
    public string CategoryFilter { get; set; } = string.Empty;
    public string StatusFilter { get; set; } = string.Empty;
    public int? RatingFilter { get; set; }
}

public class FeedbackDetailsViewModel
{
    public CustomerFeedback Feedback { get; set; } = new();
}
