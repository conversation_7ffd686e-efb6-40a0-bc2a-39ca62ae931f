using Amazon.DynamoDBv2;
using testDynamoDB.Services;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllersWithViews();

// Configure AWS DynamoDB
var dynamoDbConfig = builder.Configuration.GetSection("DynamoDB");
builder.Services.AddSingleton<IAmazonDynamoDB>(provider =>
{
    var config = new AmazonDynamoDBConfig
    {
        ServiceURL = dynamoDbConfig["ServiceURL"],
        RegionEndpoint = Amazon.RegionEndpoint.GetBySystemName(dynamoDbConfig["Region"])
    };

    return new AmazonDynamoDBClient(
        dynamoDbConfig["AccessKey"],
        dynamoDbConfig["SecretKey"],
        config
    );
});

// Register application services
builder.Services.AddScoped<IDynamoDbService, DynamoDbService>();

var app = builder.Build();

// Initialize DynamoDB table
using (var scope = app.Services.CreateScope())
{
    var dynamoDbService = scope.ServiceProvider.GetRequiredService<IDynamoDbService>();
    await dynamoDbService.CreateTableIfNotExistsAsync();
}

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseRouting();

app.UseAuthorization();

app.MapStaticAssets();

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}")
    .WithStaticAssets();


app.Run();
