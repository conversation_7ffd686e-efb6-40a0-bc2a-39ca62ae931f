using Amazon.DynamoDBv2;
using Amazon.DynamoDBv2.DataModel;
using Amazon.DynamoDBv2.Model;
using testDynamoDB.Models;

namespace testDynamoDB.Services;

public class DynamoDbService : IDynamoDbService
{
    private readonly IAmazonDynamoDB _dynamoDbClient;
    private readonly DynamoDBContext _dynamoDbContext;
    private readonly ILogger<DynamoDbService> _logger;
    private const string TableName = "CustomerFeedback";

    public DynamoDbService(IAmazonDynamoDB dynamoDbClient, ILogger<DynamoDbService> logger)
    {
        _dynamoDbClient = dynamoDbClient;
        _dynamoDbContext = new DynamoDBContext(_dynamoDbClient);
        _logger = logger;
    }

    public async Task<bool> CreateTableIfNotExistsAsync()
    {
        try
        {
            var tableResponse = await _dynamoDbClient.DescribeTableAsync(TableName);
            _logger.LogInformation($"Table {TableName} already exists");
            return true;
        }
        catch (ResourceNotFoundException)
        {
            _logger.LogInformation($"Table {TableName} does not exist. Creating...");
            
            var createTableRequest = new CreateTableRequest
            {
                TableName = TableName,
                KeySchema = new List<KeySchemaElement>
                {
                    new KeySchemaElement
                    {
                        AttributeName = "Id",
                        KeyType = KeyType.HASH
                    }
                },
                AttributeDefinitions = new List<AttributeDefinition>
                {
                    new AttributeDefinition
                    {
                        AttributeName = "Id",
                        AttributeType = ScalarAttributeType.S
                    }
                },
                BillingMode = BillingMode.PAY_PER_REQUEST
            };

            await _dynamoDbClient.CreateTableAsync(createTableRequest);
            
            // Wait for table to be created
            await WaitForTableToBeCreated(TableName);
            
            _logger.LogInformation($"Table {TableName} created successfully");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error creating table {TableName}");
            return false;
        }
    }

    private async Task WaitForTableToBeCreated(string tableName)
    {
        var maxAttempts = 30;
        var attempt = 0;
        
        while (attempt < maxAttempts)
        {
            try
            {
                var response = await _dynamoDbClient.DescribeTableAsync(tableName);
                if (response.Table.TableStatus == TableStatus.ACTIVE)
                {
                    return;
                }
            }
            catch (ResourceNotFoundException)
            {
                // Table still being created
            }
            
            await Task.Delay(1000);
            attempt++;
        }
        
        throw new TimeoutException($"Table {tableName} creation timed out");
    }

    public async Task<CustomerFeedback> CreateFeedbackAsync(CustomerFeedback feedback)
    {
        try
        {
            feedback.CreatedAt = DateTime.UtcNow;
            feedback.UpdatedAt = DateTime.UtcNow;
            
            await _dynamoDbContext.SaveAsync(feedback);
            _logger.LogInformation($"Created feedback with ID: {feedback.Id}");
            return feedback;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error creating feedback: {feedback.Id}");
            throw;
        }
    }

    public async Task<CustomerFeedback?> GetFeedbackByIdAsync(string id)
    {
        try
        {
            var feedback = await _dynamoDbContext.LoadAsync<CustomerFeedback>(id);
            return feedback;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error getting feedback by ID: {id}");
            throw;
        }
    }

    public async Task<List<CustomerFeedback>> GetAllFeedbackAsync()
    {
        try
        {
            var scanConditions = new List<ScanCondition>();
            var search = _dynamoDbContext.ScanAsync<CustomerFeedback>(scanConditions);
            var feedbacks = await search.GetRemainingAsync();
            
            return feedbacks.OrderByDescending(f => f.CreatedAt).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all feedback");
            throw;
        }
    }

    public async Task<CustomerFeedback> UpdateFeedbackAsync(CustomerFeedback feedback)
    {
        try
        {
            feedback.UpdatedAt = DateTime.UtcNow;
            await _dynamoDbContext.SaveAsync(feedback);
            _logger.LogInformation($"Updated feedback with ID: {feedback.Id}");
            return feedback;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error updating feedback: {feedback.Id}");
            throw;
        }
    }

    public async Task<bool> DeleteFeedbackAsync(string id)
    {
        try
        {
            await _dynamoDbContext.DeleteAsync<CustomerFeedback>(id);
            _logger.LogInformation($"Deleted feedback with ID: {id}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error deleting feedback: {id}");
            return false;
        }
    }

    public async Task<List<CustomerFeedback>> SearchFeedbackAsync(string searchTerm, string? category = null, string? status = null, int? rating = null)
    {
        try
        {
            var allFeedbacks = await GetAllFeedbackAsync();
            
            var filteredFeedbacks = allFeedbacks.Where(f =>
                (string.IsNullOrEmpty(searchTerm) || 
                 f.CustomerName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                 f.Email.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                 f.FeedbackText.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)) &&
                (string.IsNullOrEmpty(category) || f.Category.Equals(category, StringComparison.OrdinalIgnoreCase)) &&
                (string.IsNullOrEmpty(status) || f.Status.Equals(status, StringComparison.OrdinalIgnoreCase)) &&
                (!rating.HasValue || f.Rating == rating.Value)
            ).ToList();
            
            return filteredFeedbacks;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching feedback");
            throw;
        }
    }
}
