using testDynamoDB.Models;

namespace testDynamoDB.Services;

public interface IDynamoDbService
{
    Task<bool> CreateTableIfNotExistsAsync();
    Task<CustomerFeedback> CreateFeedbackAsync(CustomerFeedback feedback);
    Task<CustomerFeedback?> GetFeedbackByIdAsync(string id);
    Task<List<CustomerFeedback>> GetAllFeedbackAsync();
    Task<CustomerFeedback> UpdateFeedbackAsync(CustomerFeedback feedback);
    Task<bool> DeleteFeedbackAsync(string id);
    Task<List<CustomerFeedback>> SearchFeedbackAsync(string searchTerm, string? category = null, string? status = null, int? rating = null);
}
