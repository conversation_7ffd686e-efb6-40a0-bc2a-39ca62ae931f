@model CreateFeedbackViewModel
@{
    ViewData["Title"] = "Add New Feedback";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-plus-circle"></i> Add New Customer Feedback
                    </h3>
                </div>
                <div class="card-body">
                    <form asp-action="Create" method="post">
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger"></div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="CustomerName" class="form-label">Customer Name *</label>
                                <input asp-for="CustomerName" class="form-control" placeholder="Enter customer name">
                                <span asp-validation-for="CustomerName" class="text-danger"></span>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label asp-for="Email" class="form-label">Email Address *</label>
                                <input asp-for="Email" type="email" class="form-control" placeholder="Enter email address">
                                <span asp-validation-for="Email" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Category" class="form-label">Category *</label>
                                <select asp-for="Category" class="form-select">
                                    <option value="">Select a category</option>
                                    <option value="Product">Product</option>
                                    <option value="Service">Service</option>
                                    <option value="Support">Support</option>
                                    <option value="Website">Website</option>
                                    <option value="Other">Other</option>
                                </select>
                                <span asp-validation-for="Category" class="text-danger"></span>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label asp-for="Rating" class="form-label">Rating *</label>
                                <select asp-for="Rating" class="form-select">
                                    <option value="">Select a rating</option>
                                    <option value="1">1 Star - Very Poor</option>
                                    <option value="2">2 Stars - Poor</option>
                                    <option value="3">3 Stars - Average</option>
                                    <option value="4">4 Stars - Good</option>
                                    <option value="5">5 Stars - Excellent</option>
                                </select>
                                <span asp-validation-for="Rating" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="FeedbackText" class="form-label">Feedback *</label>
                            <textarea asp-for="FeedbackText" class="form-control" rows="5" 
                                      placeholder="Please share your feedback..."></textarea>
                            <span asp-validation-for="FeedbackText" class="text-danger"></span>
                            <div class="form-text">Maximum 1000 characters</div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to List
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Save Feedback
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
