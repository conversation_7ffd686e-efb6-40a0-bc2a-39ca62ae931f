@model FeedbackDetailsViewModel
@{
    ViewData["Title"] = "Delete Feedback";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle"></i> Delete Feedback
                    </h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <strong>Warning!</strong> Are you sure you want to delete this feedback? This action cannot be undone.
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h6 class="text-muted">Customer Information</h6>
                            <p><strong>Name:</strong> @Model.Feedback.CustomerName</p>
                            <p><strong>Email:</strong> @Model.Feedback.Email</p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">Feedback Information</h6>
                            <p><strong>Category:</strong> 
                                <span class="badge bg-info">@Model.Feedback.Category</span>
                            </p>
                            <p><strong>Rating:</strong>
                                @for (int i = 1; i <= 5; i++)
                                {
                                    if (i <= Model.Feedback.Rating)
                                    {
                                        <i class="fas fa-star text-warning"></i>
                                    }
                                    else
                                    {
                                        <i class="far fa-star text-muted"></i>
                                    }
                                }
                                (@Model.Feedback.Rating/5)
                            </p>
                            <p><strong>Status:</strong>
                                <span class="badge @(Model.Feedback.Status switch 
                                {
                                    "Open" => "bg-danger",
                                    "In Progress" => "bg-warning",
                                    "Resolved" => "bg-success",
                                    "Closed" => "bg-secondary",
                                    _ => "bg-primary"
                                })">
                                    @Model.Feedback.Status
                                </span>
                            </p>
                        </div>
                    </div>

                    <div class="mb-3">
                        <h6 class="text-muted">Feedback Content</h6>
                        <div class="card bg-light">
                            <div class="card-body">
                                <p class="card-text">@Model.Feedback.FeedbackText</p>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <small class="text-muted">
                            Created: @Model.Feedback.CreatedAt.ToString("MMMM dd, yyyy 'at' hh:mm tt")
                        </small>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                        <div>
                            <a asp-action="Details" asp-route-id="@Model.Feedback.Id" class="btn btn-info me-2">
                                <i class="fas fa-eye"></i> View Details
                            </a>
                            <form asp-action="Delete" method="post" class="d-inline">
                                <input type="hidden" asp-for="Feedback.Id" />
                                <button type="submit" class="btn btn-danger" 
                                        onclick="return confirm('Are you absolutely sure you want to delete this feedback?')">
                                    <i class="fas fa-trash"></i> Confirm Delete
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
