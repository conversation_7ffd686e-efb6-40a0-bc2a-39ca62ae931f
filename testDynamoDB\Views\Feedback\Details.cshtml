@model FeedbackDetailsViewModel
@{
    ViewData["Title"] = "Feedback Details";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-comment-dots"></i> Feedback Details
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5 class="text-muted">Customer Information</h5>
                            <p><strong>Name:</strong> @Model.Feedback.CustomerName</p>
                            <p><strong>Email:</strong> 
                                <a href="mailto:@Model.Feedback.Email">@Model.Feedback.Email</a>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <h5 class="text-muted">Feedback Information</h5>
                            <p><strong>Category:</strong> 
                                <span class="badge bg-info">@Model.Feedback.Category</span>
                            </p>
                            <p><strong>Rating:</strong>
                                @for (int i = 1; i <= 5; i++)
                                {
                                    if (i <= Model.Feedback.Rating)
                                    {
                                        <i class="fas fa-star text-warning"></i>
                                    }
                                    else
                                    {
                                        <i class="far fa-star text-muted"></i>
                                    }
                                }
                                (@Model.Feedback.Rating/5)
                            </p>
                            <p><strong>Status:</strong>
                                <span class="badge @(Model.Feedback.Status switch 
                                {
                                    "Open" => "bg-danger",
                                    "In Progress" => "bg-warning",
                                    "Resolved" => "bg-success",
                                    "Closed" => "bg-secondary",
                                    _ => "bg-primary"
                                })">
                                    @Model.Feedback.Status
                                </span>
                            </p>
                        </div>
                    </div>

                    <div class="mb-4">
                        <h5 class="text-muted">Feedback Content</h5>
                        <div class="card bg-light">
                            <div class="card-body">
                                <p class="card-text">@Model.Feedback.FeedbackText</p>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6 class="text-muted">Created</h6>
                            <p>@Model.Feedback.CreatedAt.ToString("MMMM dd, yyyy 'at' hh:mm tt")</p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">Last Updated</h6>
                            <p>@Model.Feedback.UpdatedAt.ToString("MMMM dd, yyyy 'at' hh:mm tt")</p>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                        <div>
                            <a asp-action="Edit" asp-route-id="@Model.Feedback.Id" class="btn btn-primary me-2">
                                <i class="fas fa-edit"></i> Edit
                            </a>
                            <a asp-action="Delete" asp-route-id="@Model.Feedback.Id" class="btn btn-danger">
                                <i class="fas fa-trash"></i> Delete
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
