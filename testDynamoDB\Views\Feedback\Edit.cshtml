@model EditFeedbackViewModel
@{
    ViewData["Title"] = "Edit Feedback";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-edit"></i> Edit Customer Feedback
                    </h3>
                </div>
                <div class="card-body">
                    <form asp-action="Edit" method="post">
                        <input asp-for="Id" type="hidden" />
                        <input asp-for="CreatedAt" type="hidden" />
                        
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger"></div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="CustomerName" class="form-label">Customer Name *</label>
                                <input asp-for="CustomerName" class="form-control">
                                <span asp-validation-for="CustomerName" class="text-danger"></span>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label asp-for="Email" class="form-label">Email Address *</label>
                                <input asp-for="Email" type="email" class="form-control">
                                <span asp-validation-for="Email" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label asp-for="Category" class="form-label">Category *</label>
                                <select asp-for="Category" class="form-select">
                                    <option value="">Select a category</option>
                                    <option value="Product">Product</option>
                                    <option value="Service">Service</option>
                                    <option value="Support">Support</option>
                                    <option value="Website">Website</option>
                                    <option value="Other">Other</option>
                                </select>
                                <span asp-validation-for="Category" class="text-danger"></span>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label asp-for="Rating" class="form-label">Rating *</label>
                                <select asp-for="Rating" class="form-select">
                                    <option value="">Select a rating</option>
                                    <option value="1">1 Star - Very Poor</option>
                                    <option value="2">2 Stars - Poor</option>
                                    <option value="3">3 Stars - Average</option>
                                    <option value="4">4 Stars - Good</option>
                                    <option value="5">5 Stars - Excellent</option>
                                </select>
                                <span asp-validation-for="Rating" class="text-danger"></span>
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label asp-for="Status" class="form-label">Status</label>
                                <select asp-for="Status" class="form-select">
                                    <option value="Open">Open</option>
                                    <option value="In Progress">In Progress</option>
                                    <option value="Resolved">Resolved</option>
                                    <option value="Closed">Closed</option>
                                </select>
                                <span asp-validation-for="Status" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="FeedbackText" class="form-label">Feedback *</label>
                            <textarea asp-for="FeedbackText" class="form-control" rows="5"></textarea>
                            <span asp-validation-for="FeedbackText" class="text-danger"></span>
                            <div class="form-text">Maximum 1000 characters</div>
                        </div>

                        <div class="mb-3">
                            <small class="text-muted">
                                Created: @Model.CreatedAt.ToString("MMM dd, yyyy 'at' hh:mm tt")
                            </small>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to List
                            </a>
                            <div>
                                <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-info me-2">
                                    <i class="fas fa-eye"></i> View Details
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Update Feedback
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
