@model FeedbackListViewModel
@{
    ViewData["Title"] = "Customer Feedback";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>Customer Feedback Management</h2>
                <a asp-action="Create" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Add New Feedback
                </a>
            </div>

            @if (TempData["Success"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    @TempData["Success"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            @if (TempData["Error"] != null)
            {
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    @TempData["Error"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            <!-- Search and Filter Form -->
            <div class="card mb-4">
                <div class="card-body">
                    <form asp-action="Index" method="get" class="row g-3">
                        <div class="col-md-3">
                            <label for="searchTerm" class="form-label">Search</label>
                            <input type="text" class="form-control" id="searchTerm" name="searchTerm" 
                                   value="@Model.SearchTerm" placeholder="Search by name, email, or feedback...">
                        </div>
                        <div class="col-md-2">
                            <label for="categoryFilter" class="form-label">Category</label>
                            <select class="form-select" id="categoryFilter" name="categoryFilter">
                                <option value="">All Categories</option>
                                <option value="Product" selected="@(Model.CategoryFilter == "Product")">Product</option>
                                <option value="Service" selected="@(Model.CategoryFilter == "Service")">Service</option>
                                <option value="Support" selected="@(Model.CategoryFilter == "Support")">Support</option>
                                <option value="Website" selected="@(Model.CategoryFilter == "Website")">Website</option>
                                <option value="Other" selected="@(Model.CategoryFilter == "Other")">Other</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="statusFilter" class="form-label">Status</label>
                            <select class="form-select" id="statusFilter" name="statusFilter">
                                <option value="">All Status</option>
                                <option value="Open" selected="@(Model.StatusFilter == "Open")">Open</option>
                                <option value="In Progress" selected="@(Model.StatusFilter == "In Progress")">In Progress</option>
                                <option value="Resolved" selected="@(Model.StatusFilter == "Resolved")">Resolved</option>
                                <option value="Closed" selected="@(Model.StatusFilter == "Closed")">Closed</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="ratingFilter" class="form-label">Rating</label>
                            <select class="form-select" id="ratingFilter" name="ratingFilter">
                                <option value="">All Ratings</option>
                                <option value="1" selected="@(Model.RatingFilter == 1)">1 Star</option>
                                <option value="2" selected="@(Model.RatingFilter == 2)">2 Stars</option>
                                <option value="3" selected="@(Model.RatingFilter == 3)">3 Stars</option>
                                <option value="4" selected="@(Model.RatingFilter == 4)">4 Stars</option>
                                <option value="5" selected="@(Model.RatingFilter == 5)">5 Stars</option>
                            </select>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-outline-primary me-2">Filter</button>
                            <a asp-action="Index" class="btn btn-outline-secondary">Clear</a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Feedback List -->
            <div class="card">
                <div class="card-body">
                    @if (Model.Feedbacks.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Customer</th>
                                        <th>Email</th>
                                        <th>Category</th>
                                        <th>Rating</th>
                                        <th>Status</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var feedback in Model.Feedbacks)
                                    {
                                        <tr>
                                            <td>@feedback.CustomerName</td>
                                            <td>@feedback.Email</td>
                                            <td>
                                                <span class="badge bg-info">@feedback.Category</span>
                                            </td>
                                            <td>
                                                @for (int i = 1; i <= 5; i++)
                                                {
                                                    if (i <= feedback.Rating)
                                                    {
                                                        <i class="fas fa-star text-warning"></i>
                                                    }
                                                    else
                                                    {
                                                        <i class="far fa-star text-muted"></i>
                                                    }
                                                }
                                            </td>
                                            <td>
                                                <span class="badge @(feedback.Status switch 
                                                {
                                                    "Open" => "bg-danger",
                                                    "In Progress" => "bg-warning",
                                                    "Resolved" => "bg-success",
                                                    "Closed" => "bg-secondary",
                                                    _ => "bg-primary"
                                                })">
                                                    @feedback.Status
                                                </span>
                                            </td>
                                            <td>@feedback.CreatedAt.ToString("MMM dd, yyyy")</td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a asp-action="Details" asp-route-id="@feedback.Id" 
                                                       class="btn btn-sm btn-outline-info" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a asp-action="Edit" asp-route-id="@feedback.Id" 
                                                       class="btn btn-sm btn-outline-primary" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a asp-action="Delete" asp-route-id="@feedback.Id" 
                                                       class="btn btn-sm btn-outline-danger" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="mt-3">
                            <p class="text-muted">
                                Showing @Model.Feedbacks.Count feedback(s)
                            </p>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">No feedback found</h4>
                            <p class="text-muted">Be the first to add customer feedback!</p>
                            <a asp-action="Create" class="btn btn-primary">Add New Feedback</a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
