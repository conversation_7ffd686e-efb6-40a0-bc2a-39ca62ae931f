{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"testDynamoDB/1.0.0": {"dependencies": {"AWSSDK.DynamoDBv2": "3.7.400.3", "AWSSDK.Extensions.NETCore.Setup": "3.7.301"}, "runtime": {"testDynamoDB.dll": {}}}, "AWSSDK.Core/3.7.400.3": {"runtime": {"lib/net8.0/AWSSDK.Core.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.7.400.3"}}}, "AWSSDK.DynamoDBv2/3.7.400.3": {"dependencies": {"AWSSDK.Core": "3.7.400.3"}, "runtime": {"lib/net8.0/AWSSDK.DynamoDBv2.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.7.400.3"}}}, "AWSSDK.Extensions.NETCore.Setup/3.7.301": {"dependencies": {"AWSSDK.Core": "3.7.400.3", "Microsoft.Extensions.Configuration.Abstractions": "2.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.0.0", "Microsoft.Extensions.Logging.Abstractions": "2.0.0"}, "runtime": {"lib/net8.0/AWSSDK.Extensions.NETCore.Setup.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.7.7.0"}}}, "Microsoft.Extensions.Configuration.Abstractions/2.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "2.0.0"}}, "Microsoft.Extensions.DependencyInjection.Abstractions/2.0.0": {}, "Microsoft.Extensions.Logging.Abstractions/2.0.0": {}, "Microsoft.Extensions.Primitives/2.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.4.0"}}, "System.Runtime.CompilerServices.Unsafe/4.4.0": {}}}, "libraries": {"testDynamoDB/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AWSSDK.Core/3.7.400.3": {"type": "package", "serviceable": true, "sha512": "sha512-Sw88+Hdw1OsA0++zImHbYJa7FA6OznFhtbB9wJjrAA5O4i/LOhqu1Zh7YjLBL4n1hkicbHSAlPmHP6Kgw70Xkg==", "path": "awssdk.core/3.7.400.3", "hashPath": "awssdk.core.3.7.400.3.nupkg.sha512"}, "AWSSDK.DynamoDBv2/3.7.400.3": {"type": "package", "serviceable": true, "sha512": "sha512-fh/q38+J3tFD8MsxmWNTPzjww1fCo9lq8FJk4OK1IcHyE5szYQnBmFoYooXqrrm6dLBj93g74+LG0cHUtLBBAA==", "path": "awssdk.dynamodbv2/3.7.400.3", "hashPath": "awssdk.dynamodbv2.3.7.400.3.nupkg.sha512"}, "AWSSDK.Extensions.NETCore.Setup/3.7.301": {"type": "package", "serviceable": true, "sha512": "sha512-hXejqa+G72kVCSmpKlNVWDEqa8KVWOFL/asgoE2mairQOnmTCv6vMxaXjJ0nQaad5/21zTdKDIfDiki5YTne1Q==", "path": "awssdk.extensions.netcore.setup/3.7.301", "hashPath": "awssdk.extensions.netcore.setup.3.7.301.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rHFrXqMIvQNq51H8RYTO4IWmDOYh8NUzyqGlh0xHWTP6XYnKk7Ryinys2uDs+Vu88b3AMlM3gBBSs78m6OQpYQ==", "path": "microsoft.extensions.configuration.abstractions/2.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.2.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eUdJ0Q/GfVyUJc0Jal5L1QZLceL78pvEM9wEKcHeI24KorqMDoVX+gWsMGLulQMfOwsUaPtkpQM2pFERTzSfSg==", "path": "microsoft.extensions.dependencyinjection.abstractions/2.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.2.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-6ZCllUYGFukkymSTx3Yr0G/ajRxoNJp7/FqSxSB4fGISST54ifBhgu4Nc0ItGi3i6DqwuNd8SUyObmiC++AO2Q==", "path": "microsoft.extensions.logging.abstractions/2.0.0", "hashPath": "microsoft.extensions.logging.abstractions.2.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ukg53qNlqTrK38WA30b5qhw0GD7y3jdI9PHHASjdKyTcBHTevFM2o23tyk3pWCgAV27Bbkm+CPQ2zUe1ZOuYSA==", "path": "microsoft.extensions.primitives/2.0.0", "hashPath": "microsoft.extensions.primitives.2.0.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-9dLLuBxr5GNmOfl2jSMcsHuteEg32BEfUotmmUkmZjpR3RpVHE8YQwt0ow3p6prwA1ME8WqDVZqrr8z6H8G+Kw==", "path": "system.runtime.compilerservices.unsafe/4.4.0", "hashPath": "system.runtime.compilerservices.unsafe.4.4.0.nupkg.sha512"}}}