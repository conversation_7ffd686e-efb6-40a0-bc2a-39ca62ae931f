{"version": 3, "targets": {"net9.0": {"AWSSDK.Core/3.7.400.3": {"type": "package", "compile": {"lib/net8.0/AWSSDK.Core.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/AWSSDK.Core.dll": {"related": ".pdb;.xml"}}}, "AWSSDK.DynamoDBv2/3.7.400.3": {"type": "package", "dependencies": {"AWSSDK.Core": "[3.7.400.3, 4.0.0)"}, "compile": {"lib/net8.0/AWSSDK.DynamoDBv2.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/AWSSDK.DynamoDBv2.dll": {"related": ".pdb;.xml"}}}, "AWSSDK.Extensions.NETCore.Setup/3.7.301": {"type": "package", "dependencies": {"AWSSDK.Core": "3.7.300", "Microsoft.Extensions.Configuration.Abstractions": "2.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.0.0", "Microsoft.Extensions.Logging.Abstractions": "2.0.0"}, "compile": {"lib/net8.0/AWSSDK.Extensions.NETCore.Setup.dll": {"related": ".pdb"}}, "runtime": {"lib/net8.0/AWSSDK.Extensions.NETCore.Setup.dll": {"related": ".pdb"}}}, "Microsoft.Extensions.Configuration.Abstractions/2.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "2.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/2.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Logging.Abstractions/2.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Primitives/2.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.4.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/4.4.0": {"type": "package", "compile": {"ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}}}, "libraries": {"AWSSDK.Core/3.7.400.3": {"sha512": "Sw88+Hdw1OsA0++zImHbYJa7FA6OznFhtbB9wJjrAA5O4i/LOhqu1Zh7YjLBL4n1hkicbHSAlPmHP6Kgw70Xkg==", "type": "package", "path": "awssdk.core/3.7.400.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "awssdk.core.3.7.400.3.nupkg.sha512", "awssdk.core.nuspec", "images/AWSLogo.png", "lib/net35/AWSSDK.Core.dll", "lib/net35/AWSSDK.Core.pdb", "lib/net35/AWSSDK.Core.xml", "lib/net45/AWSSDK.Core.dll", "lib/net45/AWSSDK.Core.pdb", "lib/net45/AWSSDK.Core.xml", "lib/net8.0/AWSSDK.Core.dll", "lib/net8.0/AWSSDK.Core.pdb", "lib/net8.0/AWSSDK.Core.xml", "lib/netcoreapp3.1/AWSSDK.Core.dll", "lib/netcoreapp3.1/AWSSDK.Core.pdb", "lib/netcoreapp3.1/AWSSDK.Core.xml", "lib/netstandard2.0/AWSSDK.Core.dll", "lib/netstandard2.0/AWSSDK.Core.pdb", "lib/netstandard2.0/AWSSDK.Core.xml", "tools/account-management.ps1"]}, "AWSSDK.DynamoDBv2/3.7.400.3": {"sha512": "fh/q38+J3tFD8MsxmWNTPzjww1fCo9lq8FJk4OK1IcHyE5szYQnBmFoYooXqrrm6dLBj93g74+LG0cHUtLBBAA==", "type": "package", "path": "awssdk.dynamodbv2/3.7.400.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "analyzers/dotnet/cs/AWSSDK.DynamoDBv2.CodeAnalysis.dll", "analyzers/dotnet/cs/SharedAnalysisCode.dll", "awssdk.dynamodbv2.3.7.400.3.nupkg.sha512", "awssdk.dynamodbv2.nuspec", "images/AWSLogo.png", "lib/net35/AWSSDK.DynamoDBv2.dll", "lib/net35/AWSSDK.DynamoDBv2.pdb", "lib/net35/AWSSDK.DynamoDBv2.xml", "lib/net45/AWSSDK.DynamoDBv2.dll", "lib/net45/AWSSDK.DynamoDBv2.pdb", "lib/net45/AWSSDK.DynamoDBv2.xml", "lib/net8.0/AWSSDK.DynamoDBv2.dll", "lib/net8.0/AWSSDK.DynamoDBv2.pdb", "lib/net8.0/AWSSDK.DynamoDBv2.xml", "lib/netcoreapp3.1/AWSSDK.DynamoDBv2.dll", "lib/netcoreapp3.1/AWSSDK.DynamoDBv2.pdb", "lib/netcoreapp3.1/AWSSDK.DynamoDBv2.xml", "lib/netstandard2.0/AWSSDK.DynamoDBv2.dll", "lib/netstandard2.0/AWSSDK.DynamoDBv2.pdb", "lib/netstandard2.0/AWSSDK.DynamoDBv2.xml", "tools/install.ps1", "tools/uninstall.ps1"]}, "AWSSDK.Extensions.NETCore.Setup/3.7.301": {"sha512": "hXejqa+G72kVCSmpKlNVWDEqa8KVWOFL/asgoE2mairQOnmTCv6vMxaXjJ0nQaad5/21zTdKDIfDiki5YTne1Q==", "type": "package", "path": "awssdk.extensions.netcore.setup/3.7.301", "files": [".nupkg.metadata", ".signature.p7s", "awssdk.extensions.netcore.setup.3.7.301.nupkg.sha512", "awssdk.extensions.netcore.setup.nuspec", "images/AWSLogo.png", "lib/net8.0/AWSSDK.Extensions.NETCore.Setup.dll", "lib/net8.0/AWSSDK.Extensions.NETCore.Setup.pdb", "lib/netcoreapp3.1/AWSSDK.Extensions.NETCore.Setup.dll", "lib/netcoreapp3.1/AWSSDK.Extensions.NETCore.Setup.pdb", "lib/netstandard2.0/AWSSDK.Extensions.NETCore.Setup.dll", "lib/netstandard2.0/AWSSDK.Extensions.NETCore.Setup.pdb"]}, "Microsoft.Extensions.Configuration.Abstractions/2.0.0": {"sha512": "rHFrXqMIvQNq51H8RYTO4IWmDOYh8NUzyqGlh0xHWTP6XYnKk7Ryinys2uDs+Vu88b3AMlM3gBBSs78m6OQpYQ==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/2.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.2.0.0.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/2.0.0": {"sha512": "eUdJ0Q/GfVyUJc0Jal5L1QZLceL78pvEM9wEKcHeI24KorqMDoVX+gWsMGLulQMfOwsUaPtkpQM2pFERTzSfSg==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/2.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.2.0.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec"]}, "Microsoft.Extensions.Logging.Abstractions/2.0.0": {"sha512": "6ZCllUYGFukkymSTx3Yr0G/ajRxoNJp7/FqSxSB4fGISST54ifBhgu4Nc0ItGi3i6DqwuNd8SUyObmiC++AO2Q==", "type": "package", "path": "microsoft.extensions.logging.abstractions/2.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.2.0.0.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec"]}, "Microsoft.Extensions.Primitives/2.0.0": {"sha512": "ukg53qNlqTrK38WA30b5qhw0GD7y3jdI9PHHASjdKyTcBHTevFM2o23tyk3pWCgAV27Bbkm+CPQ2zUe1ZOuYSA==", "type": "package", "path": "microsoft.extensions.primitives/2.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.2.0.0.nupkg.sha512", "microsoft.extensions.primitives.nuspec"]}, "System.Runtime.CompilerServices.Unsafe/4.4.0": {"sha512": "9dLLuBxr5GNmOfl2jSMcsHuteEg32BEfUotmmUkmZjpR3RpVHE8YQwt0ow3p6prwA1ME8WqDVZqrr8z6H8G+Kw==", "type": "package", "path": "system.runtime.compilerservices.unsafe/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard1.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard1.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.4.4.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt", "version.txt"]}}, "projectFileDependencyGroups": {"net9.0": ["AWSSDK.DynamoDBv2 >= 3.7.400.3", "AWSSDK.Extensions.NETCore.Setup >= 3.7.301"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\testDynamoDB\\testDynamoDB\\testDynamoDB.csproj", "projectName": "testDynamoDB", "projectPath": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\testDynamoDB\\testDynamoDB\\testDynamoDB.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\testDynamoDB\\testDynamoDB\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"AWSSDK.DynamoDBv2": {"target": "Package", "version": "[3.7.400.3, )"}, "AWSSDK.Extensions.NETCore.Setup": {"target": "Package", "version": "[3.7.301, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}}