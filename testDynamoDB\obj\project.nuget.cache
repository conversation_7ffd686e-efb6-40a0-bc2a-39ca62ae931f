{"version": 2, "dgSpecHash": "rYjLT3qJmks=", "success": true, "projectFilePath": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\testDynamoDB\\testDynamoDB\\testDynamoDB.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\awssdk.core\\3.7.400.3\\awssdk.core.3.7.400.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\awssdk.dynamodbv2\\3.7.400.3\\awssdk.dynamodbv2.3.7.400.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\awssdk.extensions.netcore.setup\\3.7.301\\awssdk.extensions.netcore.setup.3.7.301.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.abstractions\\2.0.0\\microsoft.extensions.configuration.abstractions.2.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\2.0.0\\microsoft.extensions.dependencyinjection.abstractions.2.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\2.0.0\\microsoft.extensions.logging.abstractions.2.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\2.0.0\\microsoft.extensions.primitives.2.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\4.4.0\\system.runtime.compilerservices.unsafe.4.4.0.nupkg.sha512"], "logs": []}