{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\Test\\testvs\\testDynamoDB\\testDynamoDB\\testDynamoDB.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\Test\\testvs\\testDynamoDB\\testDynamoDB\\testDynamoDB.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\testDynamoDB\\testDynamoDB\\testDynamoDB.csproj", "projectName": "testDynamoDB", "projectPath": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\testDynamoDB\\testDynamoDB\\testDynamoDB.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Test\\testvs\\testDynamoDB\\testDynamoDB\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"AWSSDK.DynamoDBv2": {"target": "Package", "version": "[3.7.400.3, )"}, "AWSSDK.Extensions.NETCore.Setup": {"target": "Package", "version": "[3.7.301, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}}}