html {
  font-size: 14px;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

.btn:focus,
.btn:active:focus,
.btn-link.nav-link:focus,
.form-control:focus,
.form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

html {
  position: relative;
  min-height: 100%;
}

body {
  margin-bottom: 60px;
}

.form-floating > .form-control-plaintext::placeholder,
.form-floating > .form-control::placeholder {
  color: var(--bs-secondary-color);
  text-align: end;
}

.form-floating > .form-control-plaintext:focus::placeholder,
.form-floating > .form-control:focus::placeholder {
  text-align: start;
}

/* Custom styles for feedback management */
.feedback-card {
  transition: transform 0.2s ease-in-out;
}

.feedback-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.rating-stars {
  color: #ffc107;
}

.status-badge {
  font-size: 0.8em;
}

.feedback-text {
  max-height: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.search-form {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.btn-group .btn {
  margin-right: 2px;
}

.table th {
  border-top: none;
  font-weight: 600;
}

.navbar-brand {
  font-weight: 600;
}

.card-header {
  font-weight: 600;
}

.footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
